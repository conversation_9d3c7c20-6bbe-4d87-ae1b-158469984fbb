<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Responsive Mega Menu</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/style.css">
</head>

<body>

  <header class="site-header">
    <div class="container">
      <a href="#" class="logo">Toko-Ku</a>

      <nav class="site-nav">
        <button class="nav-toggle" aria-label="buka navigasi" aria-expanded="false">
          <span class="hamburger"></span>
        </button>
        <ul data-visible="false">
          <li><a href="#">Home</a></li>
          <li class="has-mega-menu">
            <a href="#">Kategori</a>
            <div class="mega-menu">
              <div class="mega-menu-column">
                <h4>Makanan & Minuman</h4>
                <ul>
                  <li><a href="#">Makanan Instan</a></li>
                  <li><a href="#">Minuman Kemasan</a></li>
                  <li><a href="#">Bumbu Dapur</a></li>
                  <li><a href="#">Snack & Cemilan</a></li>
                </ul>
              </div>
              <div class="mega-menu-column">
                <h4>Kebutuhan Rumah</h4>
                <ul>
                  <li><a href="#">Pembersih Lantai</a></li>
                  <li><a href="#">Deterjen Pakaian</a></li>
                  <li><a href="#">Peralatan Masak</a></li>
                  <li><a href="#">Tisu & Kapas</a></li>
                </ul>
              </div>
              <div class="mega-menu-column">
                <h4>Perawatan Diri</h4>
                <ul>
                  <li><a href="#">Sabun & Sampo</a></li>
                  <li><a href="#">Perawatan Wajah</a></li>
                  <li><a href="#">Kesehatan Gigi</a></li>
                </ul>
              </div>
            </div>
          </li>
          <li><a href="#">Promo</a></li>
          <li><a href="#">Kontak</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <main class="container">
    <h1>Selamat Datang di Toko-Ku</h1>
    <p>Silakan jelajahi kategori produk kami melalui navigasi di atas.</p>
  </main>

  <footer class="site-footer">
    <!-- <div class="grid">
      <div class="grid-item">
        <h3>Tentang Kami</h3>
        <p>Kami adalah toko online yang menyediakan berbagai produk kebutuhan sehari-hari.</p>
      </div>
      <div class="grid-item">
        <h3>Hubungi Kami</h3>
        <p>Email: <EMAIL></p>
        <p>Telepon: 08123456789</p>
      </div>
      <div class="grid-item">
        <h3>Ikuti Kami</h3>
        <p>Facebook: @tokoku</p>
        <p>Instagram: @tokoku</p>
      </div>
      <div class="grid-item">
        <h3>Alamat</h3>
        <p>Jl. Raya No. 123, Kota, Provinsi</p>
      </div>
    </div>

    <div class="auto-grid">
      <div class="auto-grid-item">
        <h3>Tentang Kami</h3>
        <p>Kami adalah toko online yang menyediakan berbagai produk kebutuhan sehari-hari.</p>
      </div>
      <div class="auto-grid-item">
        <h3>Hubungi Kami</h3>
        <p>Email: <EMAIL></p>
        <p>Telepon: 08123456789</p>
      </div>
      <div class="auto-grid-item">
        <h3>Ikuti Kami</h3>
        <p>Facebook: @tokoku</p>
        <p>Instagram: @tokoku</p>
      </div>
      <div class="auto-grid-item">
        <h3>Alamat</h3>
        <p>Jl. Raya No. 123, Kota, Provinsi</p>
      </div>
    </div> -->

    <div class="the-stack">

    </div>
  </footer>
  <script>
    const navToggle = document.querySelector('.nav-toggle');
    const nav = document.querySelector('.site-nav ul');

    navToggle.addEventListener('click', () => {
      const isVisible = nav.getAttribute('data-visible') === 'true';
      nav.setAttribute('data-visible', !isVisible);
      navToggle.setAttribute('aria-expanded', !isVisible);
    });
  </script>
</body>

</html>