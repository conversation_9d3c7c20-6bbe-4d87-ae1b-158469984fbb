/* Reset and Basic Styles */
:root {
    --clr-primary: #1e40af;
    --clr-secondary: #f0f9ff;
    --clr-text: #334155;
    --clr-white: #ffffff;
    --header-height: 60px;
}

*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--clr-text);
    background-color: #f8fafc;
}

.container {
    width: 90%;
    max-width: 1100px;
    margin: 0 auto;
}

a {
    color: inherit;
    text-decoration: none;
}

ul {
    list-style: none;
}

/* ul li {
    display:flex;
    flex-direction: row;
    align-items: center;
    gap: 2rem;
} */

/* Header and Navigation */
.site-header {
    height: var(--header-height);
    background-color: var(--clr-secondary);
    border-bottom: 1px solid #e2e8f0;
}

.site-header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--clr-primary);
}

/* Mobile Navigation Styles */
.nav-toggle {
    display: block;
    width: 2.5rem;
    aspect-ratio: 1;
    background-color: transparent;
    border: none;
    cursor: pointer;
    z-index: 9999;
}

.hamburger {
    display: block;
    position: relative;
    width: 100%;
    height: 2px;
    background-color: var(--clr-primary);
    transition: transform 250ms ease-in-out;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: var(--clr-primary);
    transition: transform 250ms ease-in-out;
}

.hamburger::before {
    transform: translateY(-8px);
}

.hamburger::after {
    transform: translateY(8px);
}

.nav-toggle[aria-expanded="true"] .hamburger {
    transform: rotate(45deg);
}
.nav-toggle[aria-expanded="true"] .hamburger::before {
    transform: rotate(90deg) translateX(-8px);
}
.nav-toggle[aria-expanded="true"] .hamburger::after {
    opacity: 0;
}


.site-nav ul {
    position: fixed;
    inset: 0 0 0 30%;
    flex-direction: column;
    padding: min(20vh, 10rem) 2em;
    background-color: var(--clr-secondary);
    transform: translateX(100%);
    transition: transform 350ms ease-out;
    z-index: 999;
}

.site-nav ul[data-visible="true"] {
    transform: translateX(0%);
}

.site-nav ul a {
    display: block;
    padding: 1rem 0;
    font-size: 1.25rem;
    font-weight: 500;
    border-bottom: 1px solid #e2e8f0;
    transition: color 0.3s ease;
}

.site-nav ul a:hover {
    color: var(--clr-primary);
}

.mega-menu {
    display: none; /* Hidden on mobile by default */
}

/* Desktop Navigation Styles */
@media (min-width: 800px) {
    .nav-toggle {
        display: none;
    }

    .site-nav ul {
        position: static;
        display: flex;
        flex-direction: row;
        gap: 1.5rem;
        padding: 0;
        background-color: transparent;
        transform: none;
        transition: none;
        border: 1px solid black;
        min-width: 400px;
    }

    .site-nav ul a {
        position: relative;
        font-size: 1rem;
        padding: 0.5rem 0;
        border: none;
        transition: color 0.3s ease;
    }
    
    .site-nav ul a::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--clr-primary);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.3s ease-out;
    }
    
    .site-nav ul a:hover::after,
    .has-mega-menu:focus-within a::after {
        transform: scaleX(1);
        transform-origin: left;
    }
    
    .has-mega-menu {
        position: relative;
    }

    .mega-menu {
        display: grid;
        position: absolute;
        top: calc(100% + 1rem);
        left: 50%;
        transform: translate(-50%, 10px); /* Start slightly lower */
        transform-origin: top;
        opacity: 0;
        visibility: hidden;
        width: max-content;
        max-width: 650px;
        padding: 2rem;
        background-color: var(--clr-white);
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1); /* Simplified shadow */
        
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        transition: transform 0.2s ease-out, opacity 0.2s ease-out, visibility 0.2s;
    }
    
    .has-mega-menu:hover .mega-menu,
    .has-mega-menu:focus-within .mega-menu {
        opacity: 1;
        visibility: visible;
        transform: translate(-50%, 0); /* Move to final position */
    }

    .mega-menu-column h4 {
        margin-bottom: 1rem;
        color: var(--clr-primary);
    }
    
    .mega-menu-column ul a {
        padding: 0.5rem 0;
        transition: color 200ms ease;
    }

    .mega-menu-column ul a:hover {
        color: var(--clr-primary);
    }
}

.grid{
    --column-count: 3;

    display: grid;
    grid-template-columns: repeat(var(--column-count), 1fr);
    gap: 2rem;
}

.grid-item{
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.auto-grid{
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(250px, 100%), 1fr));
    gap: 2rem;
}

.the-stack{
    display: grid;
    grid-template-areas: "stack";
    place-items: center;
}

.the-stack > * {
    grid-area: stack;
}

.
}